/**
 * Valorant Random Map Picker
 * Created by <PERSON><PERSON><PERSON> (https://www.youtube.com/@Shu<PERSON><PERSON>_valorant)
 * 
 * A tool for randomly selecting Valorant maps for your next match.
 * Includes all standard maps and team deathmatch maps.
 */

document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const mapImage = document.getElementById('map-image');
    const mapName = document.getElementById('map-name');
    const standardBtn = document.getElementById('standard-btn');
    const tdmBtn = document.getElementById('tdm-btn');
    const allBtn = document.getElementById('all-btn');
    const flashContainer = document.getElementById('flash-container');
    const flashImage = document.getElementById('flash-image');
    const flashName = document.getElementById('flash-name');
    const questionPlaceholder = document.getElementById('question-placeholder');
    const staticDisplay = document.getElementById('static-display');
    const mapDisplay = document.querySelector('.map-display');
    
    // Map Data
    const standardMaps = [
        {
            name: 'Ascent',
            image: 'Standard Maps/Ascent.webp'
        },
        {
            name: 'Bind',
            image: 'Standard Maps/Bind.webp'
        },
        {
            name: 'Haven',
            image: 'Standard Maps/Haven.webp'
        },
        {
            name: 'Split',
            image: 'Standard Maps/Split.webp'
        },
        {
            name: 'Icebox',
            image: 'Standard Maps/Icebox.webp'
        },
        {
            name: 'Breeze',
            image: 'Standard Maps/Breeze.webp'
        },
        {
            name: 'Fracture',
            image: 'Standard Maps/Fracture.webp'
        },
        {
            name: 'Pearl',
            image: 'Standard Maps/Pearl.webp'
        },
        {
            name: 'Lotus',
            image: 'Standard Maps/Lotus.webp'
        },
        {
            name: 'Sunset',
            image: 'Standard Maps/Sunset.webp'
        },
        {
            name: 'Abyss',
            image: 'Standard Maps/abyss.png'
        }
    ];
    
    const tdmMaps = [
        {
            name: 'Kasbah',
            image: 'TDM Maps/Kasbah.jpg'
        },
        {
            name: 'Piazza',
            image: 'TDM Maps/Piazza.jpg'
        },
        {
            name: 'District',
            image: 'TDM Maps/District.jpg'
        },
        {
            name: 'Drift',
            image: 'TDM Maps/Drift.jpg'
        },
        {
            name: 'Glitch',
            image: 'TDM Maps/Glitch.jpg'
        }
    ];
    
    // Combine all maps
    const allMaps = [...standardMaps, ...tdmMaps];

    // Audio context for sound effects
    let audioContext;
    let isAudioEnabled = true;
    let isAnimating = false;

    /**
     * Initialize audio context (must be called after user interaction)
     */
    function initAudio() {
        if (!audioContext) {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
            } catch (error) {
                console.warn('Web Audio API not supported:', error);
                isAudioEnabled = false;
            }
        }
    }

    /**
     * Play a simple beep sound for roulette ticking
     * @param {number} frequency - Frequency of the beep
     * @param {number} duration - Duration in milliseconds
     */
    function playBeep(frequency = 800, duration = 50) {
        if (!isAudioEnabled || !audioContext) return;

        try {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            oscillator.type = 'square';

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration / 1000);
        } catch (error) {
            console.warn('Error playing sound:', error);
        }
    }

    /**
     * Play dynamic ticking sound based on current velocity
     * @param {number} velocity - Current animation velocity
     */
    function playDynamicTick(velocity) {
        if (!isAudioEnabled || !audioContext) return;

        // Map velocity to frequency and duration
        const frequency = Math.max(300, Math.min(800, 400 + (velocity * 50)));
        const duration = Math.max(20, Math.min(100, velocity * 15));

        playBeep(frequency, duration);
    }

    /**
     * Play final selection sound
     */
    function playSelectionSound() {
        if (!isAudioEnabled || !audioContext) return;

        // Play a triumphant chord-like sound
        setTimeout(() => playBeep(523, 200), 0);   // C
        setTimeout(() => playBeep(659, 200), 100); // E
        setTimeout(() => playBeep(784, 300), 200); // G
    }

    /**
     * Caches an image to localStorage as base64 data
     * @param {string} src - Image source URL
     * @param {string} mapName - Name of the map for cache key
     */
    function cacheImage(src, mapName) {
        // Check if we already have this image cached
        if (localStorage.getItem(`map_${mapName}`)) {
            return;
        }

        // Fetch the image and convert to base64
        fetch(src)
            .then(response => response.blob())
            .then(blob => {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onloadend = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(blob);
                });
            })
            .then(base64Data => {
                try {
                    localStorage.setItem(`map_${mapName}`, base64Data);
                    console.log(`Cached ${mapName} image`);
                } catch (error) {
                    // Handle localStorage quota exceeded
                    console.warn(`Failed to cache ${mapName}: ${error.message}`);
                    // If quota exceeded, remove oldest items
                    if (error.name === 'QuotaExceededError') {
                        clearOldestCache();
                        // Try again
                        localStorage.setItem(`map_${mapName}`, base64Data);
                    }
                }
            })
            .catch(error => {
                console.error(`Error caching image ${mapName}:`, error);
            });
    }

    /**
     * Removes the oldest cached images if storage is full
     */
    function clearOldestCache() {
        // Get all map cache keys
        const cacheKeys = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key.startsWith('map_')) {
                cacheKeys.push(key);
            }
        }

        // If we have cached items, remove the first one (oldest)
        if (cacheKeys.length > 0) {
            localStorage.removeItem(cacheKeys[0]);
            console.log(`Removed oldest cache: ${cacheKeys[0]}`);
        }
    }

    /**
     * Gets an image from cache or returns the original source
     * @param {string} src - Original image source URL
     * @param {string} mapName - Name of the map for cache key
     * @returns {string} - Image source (either from cache or original)
     */
    function getImageSource(src, mapName) {
        const cachedImage = localStorage.getItem(`map_${mapName}`);
        if (cachedImage) {
            return cachedImage;
        }
        
        // If not cached, we'll use the original source and cache it
        cacheImage(src, mapName);
        return src;
    }
    
    /**
     * Creates a sequence of maps for the flashing animation
     * @param {Array} maps - Array of map objects with name and image properties
     * @param {Object} selectedMap - The final map that should be selected
     */
    function createFlashSequence(maps, selectedMap) {
        const flashSequence = [];
        const cycleCount = 8; // More cycles for longer flashing effect

        // Fill the sequence with random maps from the pool
        for (let cycle = 0; cycle < cycleCount; cycle++) {
            // Shuffle maps for each cycle to create variety
            const shuffledMaps = [...maps].sort(() => Math.random() - 0.5);
            flashSequence.push(...shuffledMaps);
        }

        // Add the selected map at the end
        flashSequence.push(selectedMap);

        return flashSequence;
    }

    /**
     * Updates the flash display with a specific map
     * @param {Object} map - Map object with name and image properties
     */
    function updateFlashDisplay(map) {
        const imageSource = getImageSource(map.image, map.name);

        // Update image and name
        flashImage.src = imageSource;
        flashImage.alt = map.name;
        flashName.textContent = map.name;

        // Show the elements with fade effect
        flashImage.classList.add('visible');
        flashName.classList.add('visible');
    }

    /**
     * Hides the flash display elements
     */
    function hideFlashDisplay() {
        flashImage.classList.remove('visible');
        flashName.classList.remove('visible');
    }

    /**
     * Runs the flash animation with physics-based timing deceleration
     * @param {Array} maps - Array of map objects with name and image properties
     * @param {Object} selectedMap - The final map that should be selected
     */
    function runFlashAnimation(maps, selectedMap) {
        // Initialize audio on first interaction
        initAudio();

        // Hide static display and question placeholder, show flash container
        staticDisplay.style.display = 'none';
        questionPlaceholder.style.display = 'none';
        flashContainer.style.display = 'flex';
        mapDisplay.classList.add('roulette-active');

        // Create the flash sequence
        const flashSequence = createFlashSequence(maps, selectedMap);

        // Animation parameters
        const totalDuration = 6000; // 6 seconds total
        const startTime = performance.now();
        let currentMapIndex = 0;

        // Physics-based timing variables
        let interval = 50; // Initial interval (milliseconds between image changes)
        const friction = 1.02; // Friction coefficient (how quickly it slows down)
        const maxInterval = 800; // Maximum interval before stopping

        // Audio timing variables
        let lastTickTime = 0;

        // Start the flash animation loop
        function animateFlash() {
            const currentTime = performance.now();
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / totalDuration, 1);

            if (progress < 1 && interval < maxInterval && currentMapIndex < flashSequence.length - 1) {
                // Check if it's time to change the image
                if (currentTime - lastTickTime > interval) {
                    // Hide current display
                    hideFlashDisplay();

                    // Move to next map
                    currentMapIndex++;
                    const currentMap = flashSequence[currentMapIndex];

                    // Update display with new map
                    setTimeout(() => {
                        updateFlashDisplay(currentMap);
                    }, 50); // Brief pause for fade effect

                    // Play tick sound based on current speed
                    const velocity = 1000 / interval; // Convert interval to velocity-like value
                    playDynamicTick(velocity);

                    // Apply friction to slow down
                    interval *= friction;
                    lastTickTime = currentTime;
                }

                // Continue animation
                requestAnimationFrame(animateFlash);
            } else {
                // Animation complete - ensure we're showing the final map
                hideFlashDisplay();
                setTimeout(() => {
                    updateFlashDisplay(selectedMap);

                    // Show final result after a brief pause
                    setTimeout(() => {
                        showFinalResult(selectedMap);
                    }, 500);
                }, 100);
            }
        }

        // Start the animation with the first map
        updateFlashDisplay(flashSequence[0]);
        setTimeout(() => {
            animateFlash();
        }, 100);
    }

    /**
     * Shows the final selected map result
     * @param {Object} selectedMap - The selected map object
     */
    function showFinalResult(selectedMap) {
        // Hide flash container and show static display
        flashContainer.style.display = 'none';
        staticDisplay.style.display = 'flex';
        mapDisplay.classList.remove('roulette-active');

        // Play selection sound effect
        playSelectionSound();

        // Update static display with selected map
        const imageSource = getImageSource(selectedMap.image, selectedMap.name);
        mapImage.src = imageSource;
        mapName.textContent = selectedMap.name;

        // Add fade-in animation
        staticDisplay.classList.add('fade-in');
        setTimeout(() => {
            staticDisplay.classList.remove('fade-in');
            isAnimating = false; // Re-enable button clicks
        }, 500);

        // Update page title for better SEO
        document.title = `${selectedMap.name} - Valorant Random Map Picker | by Shushie`;

        // Add the selected map as a URL parameter without page reload (for sharing)
        const url = new URL(window.location);
        url.searchParams.set('map', selectedMap.name);
        window.history.replaceState({}, '', url);
    }

    /**
     * Selects a random map from the provided array and displays it
     * with a suspenseful flash animation
     * @param {Array} maps - Array of map objects with name and image properties
     */
    function pickRandomMap(maps) {
        // Prevent multiple animations running simultaneously
        if (isAnimating) return;

        isAnimating = true;

        // Pick random map
        const randomIndex = Math.floor(Math.random() * maps.length);
        const selectedMap = maps[randomIndex];

        // Run flash animation
        runFlashAnimation(maps, selectedMap);
    }
    
    // Initialize the display
    function initializeDisplay() {
        // Show question placeholder by default
        staticDisplay.style.display = 'flex';
        flashContainer.style.display = 'none';
        questionPlaceholder.style.display = 'flex';
    }

    // Event Listeners
    standardBtn.addEventListener('click', () => pickRandomMap(standardMaps));
    tdmBtn.addEventListener('click', () => pickRandomMap(tdmMaps));
    allBtn.addEventListener('click', () => pickRandomMap(allMaps));
    
    // Fallback for missing images
    mapImage.addEventListener('error', () => {
        mapImage.src = 'Standard Maps/abyss.png';
    });
    
    // Initialize the display
    initializeDisplay();

    // Check URL for map parameter on load
    const urlParams = new URLSearchParams(window.location.search);
    const mapParam = urlParams.get('map');
    if (mapParam) {
        // Find the map in our arrays
        const allMapsLookup = allMaps.reduce((acc, map) => {
            acc[map.name.toLowerCase()] = map;
            return acc;
        }, {});

        const requestedMap = allMapsLookup[mapParam.toLowerCase()];
        if (requestedMap) {
            // Get image source (from cache if available)
            const imageSource = getImageSource(requestedMap.image, requestedMap.name);

            // Display the requested map
            mapImage.src = imageSource;
            mapName.textContent = requestedMap.name;
            document.title = `${requestedMap.name} - Valorant Random Map Picker | by Shushie`;

            // Hide question placeholder and show the map
            questionPlaceholder.style.display = 'none';
        }
    }
}); 