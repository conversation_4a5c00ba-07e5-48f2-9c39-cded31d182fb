/**
 * Valorant Random Map Picker
 * Created by <PERSON><PERSON><PERSON> (https://www.youtube.com/@Shu<PERSON><PERSON>_valorant)
 * 
 * A tool for randomly selecting Valorant maps for your next match.
 * Includes all standard maps and team deathmatch maps.
 */

document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const mapImage = document.getElementById('map-image');
    const mapName = document.getElementById('map-name');
    const standardBtn = document.getElementById('standard-btn');
    const tdmBtn = document.getElementById('tdm-btn');
    const allBtn = document.getElementById('all-btn');
    const rouletteContainer = document.getElementById('roulette-container');
    const rouletteReel = document.getElementById('roulette-reel');
    const staticDisplay = document.getElementById('static-display');
    const mapDisplay = document.querySelector('.map-display');
    
    // Map Data
    const standardMaps = [
        {
            name: 'Ascent',
            image: 'Standard Maps/Ascent.webp'
        },
        {
            name: 'Bind',
            image: 'Standard Maps/Bind.webp'
        },
        {
            name: 'Haven',
            image: 'Standard Maps/Haven.webp'
        },
        {
            name: 'Split',
            image: 'Standard Maps/Split.webp'
        },
        {
            name: 'Icebox',
            image: 'Standard Maps/Icebox.webp'
        },
        {
            name: 'Breeze',
            image: 'Standard Maps/Breeze.webp'
        },
        {
            name: 'Fracture',
            image: 'Standard Maps/Fracture.webp'
        },
        {
            name: 'Pearl',
            image: 'Standard Maps/Pearl.webp'
        },
        {
            name: 'Lotus',
            image: 'Standard Maps/Lotus.webp'
        },
        {
            name: 'Sunset',
            image: 'Standard Maps/Sunset.webp'
        },
        {
            name: 'Abyss',
            image: 'Standard Maps/abyss.png'
        }
    ];
    
    const tdmMaps = [
        {
            name: 'Kasbah',
            image: 'TDM Maps/Kasbah.jpg'
        },
        {
            name: 'Piazza',
            image: 'TDM Maps/Piazza.jpg'
        },
        {
            name: 'District',
            image: 'TDM Maps/District.jpg'
        },
        {
            name: 'Drift',
            image: 'TDM Maps/Drift.jpg'
        },
        {
            name: 'Glitch',
            image: 'TDM Maps/Glitch.jpg'
        }
    ];
    
    // Combine all maps
    const allMaps = [...standardMaps, ...tdmMaps];

    // Audio context for sound effects
    let audioContext;
    let isAudioEnabled = true;
    let isAnimating = false;

    /**
     * Initialize audio context (must be called after user interaction)
     */
    function initAudio() {
        if (!audioContext) {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
            } catch (error) {
                console.warn('Web Audio API not supported:', error);
                isAudioEnabled = false;
            }
        }
    }

    /**
     * Play a simple beep sound for roulette ticking
     * @param {number} frequency - Frequency of the beep
     * @param {number} duration - Duration in milliseconds
     */
    function playBeep(frequency = 800, duration = 50) {
        if (!isAudioEnabled || !audioContext) return;

        try {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            oscillator.type = 'square';

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration / 1000);
        } catch (error) {
            console.warn('Error playing sound:', error);
        }
    }

    /**
     * Play roulette spinning sound effect
     */
    function playSpinSound() {
        if (!isAudioEnabled || !audioContext) return;

        // Play rapid beeps during fast spinning
        let beepCount = 0;
        const maxBeeps = 20;
        const beepInterval = setInterval(() => {
            if (beepCount >= maxBeeps) {
                clearInterval(beepInterval);
                return;
            }
            playBeep(600 + (beepCount * 20), 30);
            beepCount++;
        }, 50);
    }

    /**
     * Play final selection sound
     */
    function playSelectionSound() {
        if (!isAudioEnabled || !audioContext) return;

        // Play a triumphant chord-like sound
        setTimeout(() => playBeep(523, 200), 0);   // C
        setTimeout(() => playBeep(659, 200), 100); // E
        setTimeout(() => playBeep(784, 300), 200); // G
    }

    /**
     * Caches an image to localStorage as base64 data
     * @param {string} src - Image source URL
     * @param {string} mapName - Name of the map for cache key
     */
    function cacheImage(src, mapName) {
        // Check if we already have this image cached
        if (localStorage.getItem(`map_${mapName}`)) {
            return;
        }

        // Fetch the image and convert to base64
        fetch(src)
            .then(response => response.blob())
            .then(blob => {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onloadend = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(blob);
                });
            })
            .then(base64Data => {
                try {
                    localStorage.setItem(`map_${mapName}`, base64Data);
                    console.log(`Cached ${mapName} image`);
                } catch (error) {
                    // Handle localStorage quota exceeded
                    console.warn(`Failed to cache ${mapName}: ${error.message}`);
                    // If quota exceeded, remove oldest items
                    if (error.name === 'QuotaExceededError') {
                        clearOldestCache();
                        // Try again
                        localStorage.setItem(`map_${mapName}`, base64Data);
                    }
                }
            })
            .catch(error => {
                console.error(`Error caching image ${mapName}:`, error);
            });
    }

    /**
     * Removes the oldest cached images if storage is full
     */
    function clearOldestCache() {
        // Get all map cache keys
        const cacheKeys = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key.startsWith('map_')) {
                cacheKeys.push(key);
            }
        }

        // If we have cached items, remove the first one (oldest)
        if (cacheKeys.length > 0) {
            localStorage.removeItem(cacheKeys[0]);
            console.log(`Removed oldest cache: ${cacheKeys[0]}`);
        }
    }

    /**
     * Gets an image from cache or returns the original source
     * @param {string} src - Original image source URL
     * @param {string} mapName - Name of the map for cache key
     * @returns {string} - Image source (either from cache or original)
     */
    function getImageSource(src, mapName) {
        const cachedImage = localStorage.getItem(`map_${mapName}`);
        if (cachedImage) {
            return cachedImage;
        }
        
        // If not cached, we'll use the original source and cache it
        cacheImage(src, mapName);
        return src;
    }
    
    /**
     * Creates the roulette reel with multiple copies of maps for smooth animation
     * @param {Array} maps - Array of map objects with name and image properties
     * @param {Object} selectedMap - The final map that should be selected
     */
    function createRouletteReel(maps, selectedMap) {
        rouletteReel.innerHTML = '';

        // Create multiple copies of maps for smooth scrolling effect
        const totalMaps = maps.length * 4; // Show 4 full cycles
        const mapSequence = [];

        // Fill the sequence with random maps, ensuring the selected map appears at the end
        for (let i = 0; i < totalMaps - 1; i++) {
            const randomMap = maps[Math.floor(Math.random() * maps.length)];
            mapSequence.push(randomMap);
        }
        mapSequence.push(selectedMap); // Final map

        // Create DOM elements for each map in the sequence
        mapSequence.forEach((map, index) => {
            const mapItem = document.createElement('div');
            mapItem.className = 'roulette-item';

            const mapImg = document.createElement('img');
            mapImg.src = getImageSource(map.image, map.name);
            mapImg.alt = map.name;

            const mapLabel = document.createElement('div');
            mapLabel.className = 'map-label';
            mapLabel.textContent = map.name;

            mapItem.appendChild(mapImg);
            mapItem.appendChild(mapLabel);
            rouletteReel.appendChild(mapItem);
        });

        return mapSequence.length;
    }

    /**
     * Runs the roulette animation
     * @param {Array} maps - Array of map objects with name and image properties
     * @param {Object} selectedMap - The final map that should be selected
     */
    function runRouletteAnimation(maps, selectedMap) {
        // Initialize audio on first interaction
        initAudio();

        // Hide static display and show roulette
        staticDisplay.style.display = 'none';
        rouletteContainer.style.display = 'block';
        mapDisplay.classList.add('roulette-active');

        // Create the roulette reel
        const totalItems = createRouletteReel(maps, selectedMap);

        // Reset reel position
        rouletteReel.style.transform = 'translateX(0)';
        rouletteReel.classList.remove('roulette-spinning-fast', 'roulette-spinning');

        // Start fast spinning animation with sound
        setTimeout(() => {
            rouletteReel.classList.add('roulette-spinning-fast');
            playSpinSound(); // Play spinning sound effect
        }, 100);

        // After 1 second, switch to slower, controlled spin to final position
        setTimeout(() => {
            rouletteReel.classList.remove('roulette-spinning-fast');

            // Calculate final position (show the last item)
            const finalPosition = -((totalItems - 1) * 100);
            rouletteReel.style.transform = `translateX(${finalPosition}%)`;

            // Play slower ticking sounds during final approach
            let tickCount = 0;
            const tickInterval = setInterval(() => {
                if (tickCount >= 8) {
                    clearInterval(tickInterval);
                    return;
                }
                playBeep(400 + (tickCount * 50), 80);
                tickCount++;
            }, 250);

            // After animation completes, show final result
            setTimeout(() => {
                showFinalResult(selectedMap);
            }, 2000); // Wait for the slow animation to complete

        }, 1000);
    }

    /**
     * Shows the final selected map result
     * @param {Object} selectedMap - The selected map object
     */
    function showFinalResult(selectedMap) {
        // Hide roulette and show static display
        rouletteContainer.style.display = 'none';
        staticDisplay.style.display = 'flex';
        mapDisplay.classList.remove('roulette-active');

        // Play selection sound effect
        playSelectionSound();

        // Update static display with selected map
        const imageSource = getImageSource(selectedMap.image, selectedMap.name);
        mapImage.src = imageSource;
        mapName.textContent = selectedMap.name;

        // Add fade-in animation
        staticDisplay.classList.add('fade-in');
        setTimeout(() => {
            staticDisplay.classList.remove('fade-in');
            isAnimating = false; // Re-enable button clicks
        }, 500);

        // Update page title for better SEO
        document.title = `${selectedMap.name} - Valorant Random Map Picker | by Shushie`;

        // Add the selected map as a URL parameter without page reload (for sharing)
        const url = new URL(window.location);
        url.searchParams.set('map', selectedMap.name);
        window.history.replaceState({}, '', url);
    }

    /**
     * Selects a random map from the provided array and displays it
     * with a suspenseful roulette animation
     * @param {Array} maps - Array of map objects with name and image properties
     */
    function pickRandomMap(maps) {
        // Prevent multiple animations running simultaneously
        if (isAnimating) return;

        isAnimating = true;

        // Pick random map
        const randomIndex = Math.floor(Math.random() * maps.length);
        const selectedMap = maps[randomIndex];

        // Run roulette animation
        runRouletteAnimation(maps, selectedMap);
    }
    
    // Event Listeners
    standardBtn.addEventListener('click', () => pickRandomMap(standardMaps));
    tdmBtn.addEventListener('click', () => pickRandomMap(tdmMaps));
    allBtn.addEventListener('click', () => pickRandomMap(allMaps));
    
    // Fallback for missing images
    mapImage.addEventListener('error', () => {
        mapImage.src = 'Standard Maps/abyss.png';
    });
    
    // Check URL for map parameter on load
    const urlParams = new URLSearchParams(window.location.search);
    const mapParam = urlParams.get('map');
    if (mapParam) {
        // Find the map in our arrays
        const allMapsLookup = allMaps.reduce((acc, map) => {
            acc[map.name.toLowerCase()] = map;
            return acc;
        }, {});
        
        const requestedMap = allMapsLookup[mapParam.toLowerCase()];
        if (requestedMap) {
            // Get image source (from cache if available)
            const imageSource = getImageSource(requestedMap.image, requestedMap.name);
            
            // Display the requested map
            mapImage.src = imageSource;
            mapName.textContent = requestedMap.name;
            document.title = `${requestedMap.name} - Valorant Random Map Picker | by Shushie`;
        }
    }
}); 