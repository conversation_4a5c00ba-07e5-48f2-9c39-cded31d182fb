:root {
    --valorant-red: #ff4655;
    --valorant-dark: #111;
    --valorant-light: #ece8e1;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON>', sans-serif;
    background-color: var(--valorant-dark);
    color: var(--valorant-light);
    background-color: #0f1923;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: -1;
}

.container {
    text-align: center;
    padding: 2rem;
    max-width: 900px;
    width: 100%;
    z-index: 1;
}

.logo {
    margin-bottom: 1rem;
}

.logo img {
    max-width: 200px;
}

.valorant-title {
    font-size: 4rem;
    font-weight: 700;
    color: var(--valorant-red);
    letter-spacing: 5px;
    text-shadow: 0 0 10px rgba(255, 70, 85, 0.5);
}

h1 {
    font-size: 3rem;
    font-weight: 700;
    letter-spacing: 2px;
    margin-bottom: 2rem;
    text-shadow: 0 0 10px rgba(255, 70, 85, 0.7);
}

.map-container {
    margin-bottom: 2rem;
}

.map-display {
    position: relative;
    border: 3px solid var(--valorant-red);
    overflow: hidden;
    max-width: 800px;
    margin: 0 auto;
    height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Roulette Animation Styles */
.roulette-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: none; /* Hidden by default */
    background: rgba(0, 0, 0, 0.9);
}

.roulette-reel {
    display: flex;
    height: 100%;
    align-items: center;
    transition: transform 3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform;
}

.roulette-item {
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.roulette-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.roulette-item .map-label {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.9);
    color: var(--valorant-light);
    font-size: 2rem;
    font-weight: 700;
    text-transform: uppercase;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    border-top: 2px solid rgba(255, 70, 85, 0.6);
}

.static-display {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

#map-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

#map-name {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.7);
    color: var(--valorant-light);
    font-size: 2rem;
    font-weight: 700;
    text-transform: uppercase;
}

.buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.val-btn {
    background-color: transparent;
    color: var(--valorant-light);
    border: 2px solid var(--valorant-red);
    padding: 1rem 2rem;
    font-family: 'Oswald', sans-serif;
    font-size: 1.2rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.val-btn:hover {
    background-color: var(--valorant-red);
    border-color: var(--valorant-red);
    color: white;
}

/* Creator info and YouTube link */
.creator-info {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 70, 85, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.creator-info p {
    font-size: 1.2rem;
    color: var(--valorant-light);
}

.creator-info a {
    color: var(--valorant-red);
    text-decoration: none;
    transition: all 0.3s ease;
}

.creator-info a:hover {
    text-shadow: 0 0 8px rgba(255, 70, 85, 0.8);
}

.youtube-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: var(--valorant-red);
    color: var(--valorant-light) !important;
    padding: 0.6rem 1.2rem;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.youtube-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.5);
    text-shadow: none !important;
}

.youtube-link svg {
    width: 20px;
    height: 20px;
}

/* Animation for map change */
@keyframes mapChange {
    0% {
        opacity: 0;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.map-change {
    animation: mapChange 0.5s ease-out;
}

/* Roulette Animation Keyframes */
@keyframes rouletteSpinFast {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-300%);
    }
}

@keyframes rouletteSpinMedium {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-150%);
    }
}

@keyframes rouletteSpinSlow {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* Multi-phase roulette animation classes */
.roulette-spinning-fast {
    animation: rouletteSpinFast 1.5s linear infinite;
}

.roulette-spinning-medium {
    animation: rouletteSpinMedium 2s ease-out infinite;
}

.roulette-spinning-slow {
    animation: rouletteSpinSlow 1.5s ease-out infinite;
}

/* Final positioning with smooth deceleration */
.roulette-final-position {
    transition: transform 2s cubic-bezier(0.15, 0.25, 0.25, 1);
}

/* Fade in/out animations for switching between roulette and static display */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.fade-out {
    animation: fadeOut 0.3s ease-in-out;
}

/* Glowing effect during roulette */
.roulette-active {
    box-shadow: 0 0 20px rgba(255, 70, 85, 0.8), inset 0 0 20px rgba(255, 70, 85, 0.2);
    animation: roulettePulse 2s ease-in-out infinite;
}

/* Pulsing glow effect during animation */
@keyframes roulettePulse {
    0%, 100% {
        box-shadow: 0 0 20px rgba(255, 70, 85, 0.8), inset 0 0 20px rgba(255, 70, 85, 0.2);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 70, 85, 1), inset 0 0 30px rgba(255, 70, 85, 0.3);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .map-display {
        height: 300px;
    }

    h1 {
        font-size: 2rem;
    }

    .valorant-title {
        font-size: 3rem;
    }

    #map-name, .roulette-item .map-label {
        font-size: 1.5rem;
    }

    .val-btn {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .map-display {
        height: 220px;
    }

    .buttons {
        flex-direction: column;
    }

    .val-btn {
        width: 100%;
    }

    .roulette-item .map-label {
        font-size: 1.2rem;
        padding: 0.5rem;
    }

    /* Optimize animations for mobile performance */
    .roulette-spinning-fast {
        animation: rouletteSpinFast 1.2s linear infinite;
    }

    .roulette-spinning-medium {
        animation: rouletteSpinMedium 1.5s ease-out infinite;
    }

    .roulette-spinning-slow {
        animation: rouletteSpinSlow 1.2s ease-out infinite;
    }

    .roulette-final-position {
        transition: transform 1.5s cubic-bezier(0.15, 0.25, 0.25, 1);
    }
}

.links {
    margin: 1rem 0 2rem;
    text-align: center;
}

.switch-version {
    color: var(--valorant-light);
    text-decoration: none;
    font-size: 1rem;
    transition: color 0.2s ease;
    border-bottom: 1px solid var(--valorant-light);
    padding-bottom: 2px;
}

.switch-version:hover {
    color: var(--valorant-red);
    border-color: var(--valorant-red);
} 